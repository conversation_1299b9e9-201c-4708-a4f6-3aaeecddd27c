# Tic-Tac-Toe Game

A modern, fully-featured Tic-Tac-Toe game built with React, TypeScript, and TanStack Query.

## Features

- **Complete Game Logic**: Full implementation of Tic-Tac-Toe rules with win/draw detection
- **Modern React**: Built with React 18+ and TypeScript for type safety
- **State Management**: Uses TanStack Query for efficient state management and caching
- **Responsive Design**: Mobile-friendly interface that works on all screen sizes
- **Game Statistics**: Tracks wins, losses, and draws with local storage persistence
- **Accessibility**: Full keyboard navigation and screen reader support
- **Testing**: Comprehensive unit and integration tests with Vitest
- **Error Handling**: Robust error handling with user-friendly messages

## Technology Stack

- **Frontend**: React 18, TypeScript
- **Build Tool**: Vite
- **State Management**: TanStack Query v5
- **Testing**: Vitest, React Testing Library
- **Styling**: CSS Modules with modern CSS features
- **Package Manager**: npm

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd tic-tac-toe
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run test` - Run tests
- `npm run test:ui` - Run tests with UI
- `npm run test:coverage` - Run tests with coverage report
- `npm run lint` - Run ESLint

## Game Rules

1. The game is played on a 3×3 grid
2. Players take turns placing X and O marks
3. X always goes first
4. The first player to get 3 marks in a row (horizontally, vertically, or diagonally) wins
5. If all 9 squares are filled and no player has 3 in a row, the game is a draw

## Project Structure

```
src/
├── components/          # React components
│   ├── GameBoard/      # Main game board component
│   ├── GameCell/       # Individual cell component
│   ├── GameStatus/     # Game status display
│   └── GameControls/   # Game control buttons
├── hooks/              # Custom React hooks
│   └── useGameState.ts # TanStack Query hooks
├── services/           # API services
│   └── gameService.ts  # Game state management
├── types/              # TypeScript type definitions
│   └── game.ts         # Game-related types
├── utils/              # Utility functions
│   └── gameLogic.ts    # Core game logic
└── test/               # Test utilities and setup
```

## Architecture

The application follows a clean architecture pattern:

- **Components**: Pure React components focused on UI rendering
- **Hooks**: Custom hooks for state management using TanStack Query
- **Services**: Business logic and data persistence layer
- **Utils**: Pure functions for game logic calculations
- **Types**: Comprehensive TypeScript definitions

## Testing

The project includes comprehensive testing:

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test complete game scenarios
- **Component Tests**: Test React component behavior

Run tests with:
```bash
npm test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License.
