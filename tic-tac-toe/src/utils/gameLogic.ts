/**
 * Game logic utilities for Tic-Tac-Toe
 */

import {
  type CellValue,
  type GameState,
  type Player,
  WINNING_COMBINATIONS,
} from "../types/game";

/**
 * Check if a player has won the game
 * @param board - Current board state
 * @param player - Player to check for win
 * @returns true if player has won
 */
export const checkWinner = (board: CellValue[], player: Player): boolean => {
  return WINNING_COMBINATIONS.some((combination) =>
    combination.every((index) => board[index] === player)
  );
};

/**
 * Check if the game is a draw
 * @param board - Current board state
 * @returns true if game is a draw
 */
export const checkDraw = (board: CellValue[]): boolean => {
  return (
    board.every((cell) => cell !== null) &&
    !checkWinner(board, "X") &&
    !checkWinner(board, "O")
  );
};

/**
 * Check if a move is valid
 * @param board - Current board state
 * @param position - Position to check (0-8)
 * @returns true if move is valid
 */
export const isValidMove = (board: CellValue[], position: number): boolean => {
  return position >= 0 && position < 9 && board[position] === null;
};

/**
 * Get the next player
 * @param currentPlayer - Current player
 * @returns Next player
 */
export const getNextPlayer = (currentPlayer: Player): Player => {
  return currentPlayer === "X" ? "O" : "X";
};

/**
 * Make a move and return new game state
 * @param gameState - Current game state
 * @param position - Position to make move (0-8)
 * @returns New game state after move
 */
export const makeMove = (gameState: GameState, position: number): GameState => {
  // Validate move
  if (
    !isValidMove(gameState.board, position) ||
    gameState.status !== "playing"
  ) {
    throw new Error("Invalid move");
  }

  // Create new board with the move
  const newBoard = [...gameState.board];
  newBoard[position] = gameState.currentPlayer;

  // Check for winner
  const hasWon = checkWinner(newBoard, gameState.currentPlayer);
  const isDraw = !hasWon && checkDraw(newBoard);

  // Determine new game status
  let newStatus: GameState["status"] = "playing";
  let winner: Player | null = null;

  if (hasWon) {
    newStatus = "won";
    winner = gameState.currentPlayer;
  } else if (isDraw) {
    newStatus = "draw";
  }

  return {
    ...gameState,
    board: newBoard,
    currentPlayer:
      newStatus === "playing"
        ? getNextPlayer(gameState.currentPlayer)
        : gameState.currentPlayer,
    status: newStatus,
    winner,
    moveCount: gameState.moveCount + 1,
  };
};

/**
 * Reset game to initial state
 * @param gameId - New game ID
 * @returns Initial game state
 */
export const resetGame = (gameId: string): GameState => {
  return {
    board: Array(9).fill(null),
    currentPlayer: "X",
    status: "playing",
    winner: null,
    moveCount: 0,
    gameId,
  };
};

/**
 * Generate a unique game ID
 * @returns Unique game ID
 */
export const generateGameId = (): string => {
  return `game_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
