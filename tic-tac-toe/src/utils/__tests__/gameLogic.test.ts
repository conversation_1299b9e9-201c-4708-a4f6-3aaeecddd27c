/**
 * Unit tests for game logic utilities
 */

import { describe, expect, it } from "vitest";
import {
  type CellValue,
  type GameState,
  INITIAL_GAME_STATE,
} from "../../types/game";
import {
  checkDraw,
  checkWinner,
  generateGameId,
  getNextPlayer,
  isValidMove,
  makeMove,
  resetGame,
} from "../gameLogic";

describe("gameLogic", () => {
  describe("checkWinner", () => {
    it("should detect horizontal wins", () => {
      const board: CellValue[] = [
        "X",
        "X",
        "X",
        null,
        null,
        null,
        null,
        null,
        null,
      ];
      expect(checkWinner(board, "X")).toBe(true);
      expect(checkWinner(board, "O")).toBe(false);
    });

    it("should detect vertical wins", () => {
      const board: CellValue[] = [
        "X",
        null,
        null,
        "X",
        null,
        null,
        "X",
        null,
        null,
      ];
      expect(checkWinner(board, "X")).toBe(true);
      expect(checkWinner(board, "O")).toBe(false);
    });

    it("should detect diagonal wins", () => {
      const board: CellValue[] = [
        "X",
        null,
        null,
        null,
        "X",
        null,
        null,
        null,
        "X",
      ];
      expect(checkWinner(board, "X")).toBe(true);
      expect(checkWinner(board, "O")).toBe(false);
    });

    it("should return false when no winner", () => {
      const board: CellValue[] = ["X", "O", "X", "O", "X", "O", "O", "X", "O"];
      expect(checkWinner(board, "X")).toBe(false);
      expect(checkWinner(board, "O")).toBe(false);
    });
  });

  describe("checkDraw", () => {
    it("should detect draw when board is full and no winner", () => {
      const board: CellValue[] = ["X", "O", "X", "O", "X", "O", "O", "X", "O"];
      expect(checkDraw(board)).toBe(true);
    });

    it("should return false when board is not full", () => {
      const board: CellValue[] = ["X", "O", "X", null, "X", "O", "O", "X", "O"];
      expect(checkDraw(board)).toBe(false);
    });

    it("should return false when there is a winner", () => {
      const board: CellValue[] = [
        "X",
        "X",
        "X",
        "O",
        "O",
        null,
        null,
        null,
        null,
      ];
      expect(checkDraw(board)).toBe(false);
    });
  });

  describe("isValidMove", () => {
    it("should return true for valid moves", () => {
      const board = Array(9).fill(null);
      expect(isValidMove(board, 0)).toBe(true);
      expect(isValidMove(board, 4)).toBe(true);
      expect(isValidMove(board, 8)).toBe(true);
    });

    it("should return false for occupied positions", () => {
      const board: CellValue[] = [
        "X",
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
      ];
      expect(isValidMove(board, 0)).toBe(false);
    });

    it("should return false for invalid positions", () => {
      const board = Array(9).fill(null);
      expect(isValidMove(board, -1)).toBe(false);
      expect(isValidMove(board, 9)).toBe(false);
    });
  });

  describe("getNextPlayer", () => {
    it("should alternate between X and O", () => {
      expect(getNextPlayer("X")).toBe("O");
      expect(getNextPlayer("O")).toBe("X");
    });
  });

  describe("makeMove", () => {
    it("should make a valid move", () => {
      const gameState: GameState = {
        ...INITIAL_GAME_STATE,
        gameId: "test-game",
      };

      const newState = makeMove(gameState, 0);

      expect(newState.board[0]).toBe("X");
      expect(newState.currentPlayer).toBe("O");
      expect(newState.moveCount).toBe(1);
      expect(newState.status).toBe("playing");
    });

    it("should detect win condition", () => {
      const gameState: GameState = {
        ...INITIAL_GAME_STATE,
        board: ["X", "X", null, null, null, null, null, null, null],
        gameId: "test-game",
      };

      const newState = makeMove(gameState, 2);

      expect(newState.status).toBe("won");
      expect(newState.winner).toBe("X");
    });

    it("should detect draw condition", () => {
      const gameState: GameState = {
        ...INITIAL_GAME_STATE,
        board: ["X", "O", "X", "O", "X", "O", "O", "X", null],
        currentPlayer: "O",
        moveCount: 8,
        gameId: "test-game",
      };

      const newState = makeMove(gameState, 8);

      expect(newState.status).toBe("draw");
      expect(newState.winner).toBe(null);
    });

    it("should throw error for invalid moves", () => {
      const gameState: GameState = {
        ...INITIAL_GAME_STATE,
        board: ["X", null, null, null, null, null, null, null, null],
        gameId: "test-game",
      };

      expect(() => makeMove(gameState, 0)).toThrow("Invalid move");
    });

    it("should throw error when game is over", () => {
      const gameState: GameState = {
        ...INITIAL_GAME_STATE,
        status: "won",
        winner: "X",
        gameId: "test-game",
      };

      expect(() => makeMove(gameState, 1)).toThrow("Invalid move");
    });
  });

  describe("resetGame", () => {
    it("should reset game to initial state", () => {
      const gameId = "new-game-123";
      const newState = resetGame(gameId);

      expect(newState.board).toEqual(Array(9).fill(null));
      expect(newState.currentPlayer).toBe("X");
      expect(newState.status).toBe("playing");
      expect(newState.winner).toBe(null);
      expect(newState.moveCount).toBe(0);
      expect(newState.gameId).toBe(gameId);
    });
  });

  describe("generateGameId", () => {
    it("should generate unique game IDs", () => {
      const id1 = generateGameId();
      const id2 = generateGameId();

      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^game_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^game_\d+_[a-z0-9]+$/);
    });
  });
});
