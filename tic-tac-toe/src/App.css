/* Main App styles */

.app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
}

.app__container {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 2rem;
  max-width: 600px;
  width: 100%;
}

.app__header {
  text-align: center;
  margin-bottom: 2rem;
}

.app__title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.app__main {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.app__loading {
  text-align: center;
  font-size: 1.25rem;
  color: #6b7280;
  padding: 2rem;
}

.app__error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  text-align: center;
}

.app__retry-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 1rem;
}

.app__retry-button:hover {
  background-color: #2563eb;
}

/* Responsive design */
@media (max-width: 640px) {
  .app {
    padding: 0.5rem;
  }

  .app__container {
    padding: 1rem;
    border-radius: 12px;
  }

  .app__title {
    font-size: 2rem;
  }
}
