/**
 * Main App component for Tic-Tac-Toe game
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import React from 'react';
import './App.css';
import { GameBoard } from './components/GameBoard';
import { GameControls } from './components/GameControls';
import { GameStatus } from './components/GameStatus';
import { useClearGameData, useGameMove, useGameState, useGameStats, useResetGame } from './hooks/useGameState';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const GameApp: React.FC = () => {
  // Game state and mutations
  const { data: gameState, isLoading: isGameLoading, error: gameError } = useGameState();
  const { data: gameStats, isLoading: isStatsLoading } = useGameStats();
  const gameMoveMutation = useGameMove();
  const resetGameMutation = useResetGame();
  const clearDataMutation = useClearGameData();

  // Handle cell click
  const handleCellClick = (position: number) => {
    if (gameState && gameState.status === 'playing') {
      gameMoveMutation.mutate({ gameState, position });
    }
  };

  // Handle game reset
  const handleReset = () => {
    resetGameMutation.mutate();
  };

  // Handle clear all data
  const handleClearData = () => {
    if (window.confirm('Are you sure you want to clear all game data? This cannot be undone.')) {
      clearDataMutation.mutate();
    }
  };

  // Loading state
  if (isGameLoading || !gameState) {
    return (
      <div className="app">
        <div className="app__container">
          <h1 className="app__title">Tic-Tac-Toe</h1>
          <div className="app__loading">Loading game...</div>
        </div>
      </div>
    );
  }

  // Error state
  if (gameError) {
    return (
      <div className="app">
        <div className="app__container">
          <h1 className="app__title">Tic-Tac-Toe</h1>
          <div className="app__error">
            Error loading game: {gameError.message}
          </div>
          <button onClick={handleReset} className="app__retry-button">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const isGameDisabled =
    gameMoveMutation.isPending ||
    resetGameMutation.isPending ||
    clearDataMutation.isPending ||
    gameState.status !== 'playing';

  return (
    <div className="app">
      <div className="app__container">
        <header className="app__header">
          <h1 className="app__title">Tic-Tac-Toe</h1>
        </header>

        <main className="app__main">
          <GameStatus
            gameState={gameState}
            isLoading={gameMoveMutation.isPending}
          />

          <GameBoard
            gameState={gameState}
            disabled={isGameDisabled}
            onCellClick={handleCellClick}
          />

          <GameControls
            onReset={handleReset}
            onClearData={handleClearData}
            disabled={resetGameMutation.isPending || clearDataMutation.isPending}
            gameStats={gameStats}
            isStatsLoading={isStatsLoading}
          />
        </main>

        {/* Display mutation errors */}
        {gameMoveMutation.error && (
          <div className="app__error">
            Move error: {gameMoveMutation.error.message}
          </div>
        )}

        {resetGameMutation.error && (
          <div className="app__error">
            Reset error: {resetGameMutation.error.message}
          </div>
        )}

        {clearDataMutation.error && (
          <div className="app__error">
            Clear data error: {clearDataMutation.error.message}
          </div>
        )}
      </div>
    </div>
  );
};

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <GameApp />
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

export default App;
