/**
 * Integration test for game logic
 */

import { describe, it, expect } from 'vitest';
import { makeMove, resetGame, generateGameId } from '../utils/gameLogic';

describe('Game Integration Tests', () => {
  it('should play a complete game', () => {
    // Start a new game
    let gameState = resetGame(generateGameId());
    expect(gameState.currentPlayer).toBe('X');
    expect(gameState.status).toBe('playing');

    // X makes first move
    gameState = makeMove(gameState, 0); // Top-left
    expect(gameState.board[0]).toBe('X');
    expect(gameState.currentPlayer).toBe('O');

    // O makes move
    gameState = makeMove(gameState, 4); // Center
    expect(gameState.board[4]).toBe('O');
    expect(gameState.currentPlayer).toBe('X');

    // X makes move
    gameState = makeMove(gameState, 1); // Top-center
    expect(gameState.board[1]).toBe('X');

    // O makes move
    gameState = makeMove(gameState, 8); // Bottom-right
    expect(gameState.board[8]).toBe('O');

    // X wins with top row
    gameState = makeMove(gameState, 2); // Top-right
    expect(gameState.status).toBe('won');
    expect(gameState.winner).toBe('X');
  });

  it('should handle draw game', () => {
    let gameState = resetGame(generateGameId());
    
    // Play moves that result in a draw
    const moves = [0, 1, 2, 4, 3, 5, 7, 6, 8]; // X: 0,2,3,7,8  O: 1,4,5,6
    
    moves.forEach((position, index) => {
      if (gameState.status === 'playing') {
        gameState = makeMove(gameState, position);
      }
    });
    
    expect(gameState.status).toBe('draw');
    expect(gameState.winner).toBe(null);
  });
});
