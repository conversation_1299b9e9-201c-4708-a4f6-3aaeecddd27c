/**
 * GameControls component - provides game control buttons and statistics
 */

import React from 'react';
import { type GameStats } from '../../types/game';
import './GameControls.css';

interface GameControlsProps {
  /** Callback to reset the game */
  onReset: () => void;
  /** Callback to clear all game data */
  onClearData?: () => void;
  /** Whether controls are disabled */
  disabled?: boolean;
  /** Game statistics */
  gameStats?: GameStats;
  /** Whether stats are loading */
  isStatsLoading?: boolean;
}

export const GameControls: React.FC<GameControlsProps> = ({
  onReset,
  onClearData,
  disabled = false,
  gameStats,
  isStatsLoading = false,
}) => {
  return (
    <div className="game-controls" data-testid="game-controls">
      <div className="game-controls__buttons">
        <button
          className="game-controls__button game-controls__button--primary"
          onClick={onReset}
          disabled={disabled}
          data-testid="reset-button"
        >
          New Game
        </button>

        {onClearData && (
          <button
            className="game-controls__button game-controls__button--secondary"
            onClick={onClearData}
            disabled={disabled}
            data-testid="clear-data-button"
          >
            Clear All Data
          </button>
        )}
      </div>

      {gameStats && (
        <div className="game-controls__stats">
          <h3 className="game-controls__stats-title">Game Statistics</h3>

          {isStatsLoading ? (
            <div className="game-controls__stats-loading">Loading stats...</div>
          ) : (
            <div className="game-controls__stats-grid">
              <div className="game-controls__stat">
                <span className="game-controls__stat-label">Total Games</span>
                <span className="game-controls__stat-value">{gameStats.totalGames}</span>
              </div>

              <div className="game-controls__stat">
                <span className="game-controls__stat-label">X Wins</span>
                <span className="game-controls__stat-value game-controls__stat-value--x">
                  {gameStats.xWins}
                </span>
              </div>

              <div className="game-controls__stat">
                <span className="game-controls__stat-label">O Wins</span>
                <span className="game-controls__stat-value game-controls__stat-value--o">
                  {gameStats.oWins}
                </span>
              </div>

              <div className="game-controls__stat">
                <span className="game-controls__stat-label">Draws</span>
                <span className="game-controls__stat-value game-controls__stat-value--draw">
                  {gameStats.draws}
                </span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
