/* GameControls component styles */

.game-controls {
  text-align: center;
  padding: 1rem;
}

.game-controls__buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.game-controls__button {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.game-controls__button:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

.game-controls__button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.game-controls__button--primary {
  background-color: #3b82f6;
  color: white;
}

.game-controls__button--primary:hover:not(:disabled) {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.game-controls__button--secondary {
  background-color: #6b7280;
  color: white;
}

.game-controls__button--secondary:hover:not(:disabled) {
  background-color: #4b5563;
  transform: translateY(-1px);
}

.game-controls__stats {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  max-width: 400px;
  margin: 0 auto;
}

.game-controls__stats-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
}

.game-controls__stats-loading {
  color: #6b7280;
  font-style: italic;
}

.game-controls__stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.game-controls__stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.game-controls__stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.game-controls__stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #374151;
}

.game-controls__stat-value--x {
  color: #dc2626;
}

.game-controls__stat-value--o {
  color: #2563eb;
}

.game-controls__stat-value--draw {
  color: #f59e0b;
}

/* Responsive design */
@media (max-width: 480px) {
  .game-controls {
    padding: 0.5rem;
  }
  
  .game-controls__buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }
  
  .game-controls__button {
    width: 100%;
    max-width: 200px;
  }
  
  .game-controls__stats {
    padding: 1rem;
  }
  
  .game-controls__stats-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}
