/**
 * GameStatus component - displays current game status and player information
 * Optimized with React.memo and useMemo to prevent unnecessary re-renders
 */

import React, { useMemo } from 'react';
import { type GameState } from '../../types/game';
import './GameStatus.css';

interface GameStatusProps {
  /** Current game state */
  gameState: GameState;
  /** Whether the game is loading */
  isLoading?: boolean;
}

/**
 * Optimized GameStatus component with memoization to prevent unnecessary re-renders
 */
export const GameStatus: React.FC<GameStatusProps> = React.memo(({
  gameState,
  isLoading = false,
}) => {
  // Memoize status message to prevent recalculation on every render
  const statusMessage = useMemo((): string => {
    if (isLoading) {
      return 'Loading...';
    }

    switch (gameState.status) {
      case 'playing':
        return `Player ${gameState.currentPlayer}'s turn`;
      case 'won':
        return `Player ${gameState.winner} wins!`;
      case 'draw':
        return "It's a draw!";
      default:
        return 'Game ready';
    }
  }, [isLoading, gameState.status, gameState.currentPlayer, gameState.winner]);

  // Memoize CSS class to prevent recalculation on every render
  const statusClass = useMemo((): string => {
    const baseClass = 'game-status__message';

    if (isLoading) {
      return `${baseClass} game-status__message--loading`;
    }

    switch (gameState.status) {
      case 'playing':
        return `${baseClass} game-status__message--playing`;
      case 'won':
        return `${baseClass} game-status__message--won`;
      case 'draw':
        return `${baseClass} game-status__message--draw`;
      default:
        return baseClass;
    }
  }, [isLoading, gameState.status]);

  // Memoize whether to show move count to prevent unnecessary re-renders
  const shouldShowMoveCount = useMemo(() => {
    return gameState.moveCount > 0 && !isLoading;
  }, [gameState.moveCount, isLoading]);

  return (
    <div className="game-status" data-testid="game-status">
      <div className={statusClass}>
        {statusMessage}
      </div>

      {shouldShowMoveCount && (
        <div className="game-status__info">
          <span className="game-status__moves">
            Moves: {gameState.moveCount}
          </span>
        </div>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for React.memo
  // Only re-render if relevant props have actually changed
  return (
    prevProps.isLoading === nextProps.isLoading &&
    prevProps.gameState.status === nextProps.gameState.status &&
    prevProps.gameState.currentPlayer === nextProps.gameState.currentPlayer &&
    prevProps.gameState.winner === nextProps.gameState.winner &&
    prevProps.gameState.moveCount === nextProps.gameState.moveCount
  );
});
