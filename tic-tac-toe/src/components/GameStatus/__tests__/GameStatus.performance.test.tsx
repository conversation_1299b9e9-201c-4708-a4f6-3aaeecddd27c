/**
 * Performance tests for GameStatus component
 * Tests to verify that optimizations prevent unnecessary re-renders
 */

import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { GameStatus } from '../';
import { type GameState } from '../../../types/game';

// Simple performance test without complex mocking

describe('GameStatus Performance', () => {
  const baseGameState: GameState = {
    board: Array(9).fill(null),
    currentPlayer: 'X',
    status: 'playing',
    winner: null,
    moveCount: 0,
    gameId: 'test-game',
  };

  it('should render correctly with optimized structure', () => {
    const { rerender } = render(<GameStatus gameState={baseGameState} />);

    // Verify initial render
    expect(screen.getByText("Player X's turn")).toBeInTheDocument();
    expect(screen.getByTestId('game-status')).toBeInTheDocument();

    // Re-render with same props should work without issues
    rerender(<GameStatus gameState={baseGameState} />);
    expect(screen.getByText("Player X's turn")).toBeInTheDocument();
  });

  it('should handle state changes smoothly', () => {
    const { rerender } = render(<GameStatus gameState={baseGameState} />);

    // Change to O player
    const gameStateWithO = {
      ...baseGameState,
      currentPlayer: 'O' as const,
    };
    rerender(<GameStatus gameState={gameStateWithO} />);
    expect(screen.getByText("Player O's turn")).toBeInTheDocument();

    // Change to won state
    const wonGameState = {
      ...baseGameState,
      status: 'won' as const,
      winner: 'X' as const,
    };
    rerender(<GameStatus gameState={wonGameState} />);
    expect(screen.getByText('Player X wins!')).toBeInTheDocument();
  });

  it('should handle loading state transitions smoothly', () => {
    const { rerender } = render(<GameStatus gameState={baseGameState} isLoading={false} />);

    expect(screen.getByText("Player X's turn")).toBeInTheDocument();

    // Change to loading
    rerender(<GameStatus gameState={baseGameState} isLoading={true} />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();

    // Change back to not loading
    rerender(<GameStatus gameState={baseGameState} isLoading={false} />);
    expect(screen.getByText("Player X's turn")).toBeInTheDocument();
  });

  it('should handle move count changes without flickering', () => {
    const { rerender } = render(<GameStatus gameState={baseGameState} />);

    // No move count initially
    expect(screen.queryByText('Moves:')).not.toBeInTheDocument();

    // Add moves
    const gameStateWithMoves = {
      ...baseGameState,
      moveCount: 3,
    };
    rerender(<GameStatus gameState={gameStateWithMoves} />);
    expect(screen.getByText('Moves: 3')).toBeInTheDocument();

    // Update move count
    const gameStateWithMoreMoves = {
      ...baseGameState,
      moveCount: 5,
    };
    rerender(<GameStatus gameState={gameStateWithMoreMoves} />);
    expect(screen.getByText('Moves: 5')).toBeInTheDocument();
  });

  it('should maintain consistent CSS classes', () => {
    const { rerender } = render(<GameStatus gameState={baseGameState} />);

    const messageElement = screen.getByText("Player X's turn");
    expect(messageElement).toHaveClass('game-status__message--playing');

    // Re-render with same state should maintain same classes
    rerender(<GameStatus gameState={baseGameState} />);
    const messageElementAfterRerender = screen.getByText("Player X's turn");
    expect(messageElementAfterRerender).toHaveClass('game-status__message--playing');
  });
});
