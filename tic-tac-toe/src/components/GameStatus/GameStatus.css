/* GameStatus component styles */

.game-status {
  text-align: center;
  padding: 1rem;
  margin-bottom: 1rem;
}

.game-status__message {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  /* Prevent layout shifts by maintaining consistent dimensions */
  min-height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  /* Use transform for animations to avoid layout recalculation */
  will-change: transform;
}

.game-status__message--playing {
  background-color: #dbeafe;
  color: #1e40af;
  border: 2px solid #3b82f6;
}

.game-status__message--won {
  background-color: #dcfce7;
  color: #166534;
  border: 2px solid #22c55e;
  animation: celebration 0.6s ease-in-out;
}

.game-status__message--draw {
  background-color: #fef3c7;
  color: #92400e;
  border: 2px solid #f59e0b;
}

.game-status__message--loading {
  background-color: #f3f4f6;
  color: #6b7280;
  border: 2px solid #d1d5db;
}

.game-status__info {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

.game-status__moves {
  font-size: 0.875rem;
  color: #6b7280;
  background-color: #f9fafb;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

@keyframes celebration {
  0%, 100% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.02);
  }
  50% {
    transform: scale(1.05);
  }
  75% {
    transform: scale(1.02);
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .game-status__message {
    transition: none;
    animation: none;
  }

  .game-status__message--won {
    animation: none;
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .game-status {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .game-status__message {
    font-size: 1.25rem;
    padding: 0.5rem 1rem;
  }
}
