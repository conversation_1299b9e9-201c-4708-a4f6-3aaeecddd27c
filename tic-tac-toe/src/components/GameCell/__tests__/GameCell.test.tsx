/**
 * Unit tests for GameCell component
 */

import { fireEvent, render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { GameCell } from '../';

describe('GameCell', () => {
  const defaultProps = {
    value: null,
    position: 0,
    disabled: false,
    onClick: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render empty cell', () => {
    render(<GameCell {...defaultProps} />);

    const cell = screen.getByTestId('game-cell-0');
    expect(cell).toBeInTheDocument();
    expect(cell).toHaveTextContent('');
  });

  it('should render cell with X value', () => {
    render(<GameCell {...defaultProps} value="X" />);

    const cell = screen.getByTestId('game-cell-0');
    expect(cell).toHaveTextContent('X');
    expect(cell).toHaveClass('game-cell--x');
  });

  it('should render cell with O value', () => {
    render(<GameCell {...defaultProps} value="O" />);

    const cell = screen.getByTestId('game-cell-0');
    expect(cell).toHaveTextContent('O');
    expect(cell).toHaveClass('game-cell--o');
  });

  it('should call onClick when empty cell is clicked', () => {
    const onClick = vi.fn();
    render(<GameCell {...defaultProps} onClick={onClick} />);

    const cell = screen.getByTestId('game-cell-0');
    fireEvent.click(cell);

    expect(onClick).toHaveBeenCalledWith(0);
  });

  it('should not call onClick when occupied cell is clicked', () => {
    const onClick = vi.fn();
    render(<GameCell {...defaultProps} value="X" onClick={onClick} />);

    const cell = screen.getByTestId('game-cell-0');
    fireEvent.click(cell);

    expect(onClick).not.toHaveBeenCalled();
  });

  it('should not call onClick when disabled', () => {
    const onClick = vi.fn();
    render(<GameCell {...defaultProps} disabled={true} onClick={onClick} />);

    const cell = screen.getByTestId('game-cell-0');
    fireEvent.click(cell);

    expect(onClick).not.toHaveBeenCalled();
  });

  it('should have winning class when isWinning is true', () => {
    render(<GameCell {...defaultProps} value="X" isWinning={true} />);

    const cell = screen.getByTestId('game-cell-0');
    expect(cell).toHaveClass('game-cell--winning');
  });

  it('should have correct accessibility attributes', () => {
    render(<GameCell {...defaultProps} />);

    const cell = screen.getByTestId('game-cell-0');
    expect(cell).toHaveAttribute('aria-label', 'Cell 1, empty');
  });

  it('should have correct accessibility attributes for occupied cell', () => {
    render(<GameCell {...defaultProps} value="X" />);

    const cell = screen.getByTestId('game-cell-0');
    expect(cell).toHaveAttribute('aria-label', 'Cell 1, contains X');
  });
});
