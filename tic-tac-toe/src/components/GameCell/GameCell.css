/* GameCell component styles */

.game-cell {
  width: 100px;
  height: 100px;
  border: 2px solid #333;
  background-color: #fff;
  font-size: 2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  outline: none;
}

.game-cell:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

.game-cell--clickable:hover {
  background-color: #f8f9fa;
  transform: scale(1.02);
}

.game-cell--disabled {
  cursor: not-allowed;
}

.game-cell--x {
  color: #dc2626;
}

.game-cell--o {
  color: #2563eb;
}

.game-cell--winning {
  background-color: #fef3c7;
  animation: winning-pulse 1s ease-in-out infinite alternate;
}

.game-cell__content {
  display: block;
  line-height: 1;
  user-select: none;
}

@keyframes winning-pulse {
  from {
    background-color: #fef3c7;
  }
  to {
    background-color: #fbbf24;
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .game-cell {
    width: 80px;
    height: 80px;
    font-size: 1.5rem;
  }
}

@media (max-width: 360px) {
  .game-cell {
    width: 70px;
    height: 70px;
    font-size: 1.25rem;
  }
}
