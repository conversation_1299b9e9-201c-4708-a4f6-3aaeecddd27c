/**
 * GameCell component - represents a single cell in the tic-tac-toe board
 */

import React from 'react';
import { type CellValue } from '../../types/game';
import './GameCell.css';

interface GameCellProps {
  /** Value of the cell (X, O, or null) */
  value: CellValue;
  /** Position of the cell (0-8) */
  position: number;
  /** Whether the cell is clickable */
  disabled: boolean;
  /** Callback when cell is clicked */
  onClick: (position: number) => void;
  /** Whether this cell is part of winning combination */
  isWinning?: boolean;
}

export const GameCell: React.FC<GameCellProps> = ({
  value,
  position,
  disabled,
  onClick,
  isWinning = false,
}) => {
  const handleClick = () => {
    if (!disabled && !value) {
      onClick(position);
    }
  };

  const cellClasses = [
    'game-cell',
    value ? `game-cell--${value.toLowerCase()}` : '',
    disabled ? 'game-cell--disabled' : 'game-cell--clickable',
    isWinning ? 'game-cell--winning' : '',
  ].filter(Boolean).join(' ');

  return (
    <button
      className={cellClasses}
      onClick={handleClick}
      disabled={disabled || !!value}
      aria-label={`Cell ${position + 1}${value ? `, contains ${value}` : ', empty'}`}
      data-testid={`game-cell-${position}`}
    >
      <span className="game-cell__content">
        {value}
      </span>
    </button>
  );
};
