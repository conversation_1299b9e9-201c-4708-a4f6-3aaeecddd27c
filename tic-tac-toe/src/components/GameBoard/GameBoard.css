/* GameBoard component styles */

.game-board {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.game-board__grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 2px;
  background-color: #333;
  padding: 2px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 480px) {
  .game-board {
    padding: 0.5rem;
  }
  
  .game-board__grid {
    gap: 1px;
    padding: 1px;
  }
}
