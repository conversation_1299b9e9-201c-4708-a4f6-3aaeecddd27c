/**
 * GameBoard component - renders the 3x3 tic-tac-toe board
 */

import React from 'react';
import { type GameState, WINNING_COMBINATIONS } from '../../types/game';
import { GameCell } from '../GameCell';
import './GameBoard.css';

interface GameBoardProps {
  /** Current game state */
  gameState: GameState;
  /** Whether the board is disabled (game over or loading) */
  disabled: boolean;
  /** Callback when a cell is clicked */
  onCellClick: (position: number) => void;
}

export const GameBoard: React.FC<GameBoardProps> = ({
  gameState,
  disabled,
  onCellClick,
}) => {
  // Find winning combination if game is won
  const winningCombination = React.useMemo(() => {
    if (gameState.status === 'won' && gameState.winner) {
      return WINNING_COMBINATIONS.find(combination =>
        combination.every(index => gameState.board[index] === gameState.winner)
      ) || null;
    }
    return null;
  }, [gameState.status, gameState.winner, gameState.board]);

  return (
    <div className="game-board" data-testid="game-board">
      <div className="game-board__grid">
        {gameState.board.map((cellValue, index) => (
          <GameCell
            key={index}
            value={cellValue}
            position={index}
            disabled={disabled}
            onClick={onCellClick}
            isWinning={winningCombination?.includes(index as never) || false}
          />
        ))}
      </div>
    </div>
  );
};
