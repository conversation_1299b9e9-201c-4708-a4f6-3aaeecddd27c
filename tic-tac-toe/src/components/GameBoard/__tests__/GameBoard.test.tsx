/**
 * Unit tests for GameBoard component
 */

import { fireEvent, render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { GameBoard } from '../';
import { GameState } from '../../../types/game';

describe('GameBoard', () => {
  const mockGameState: GameState = {
    board: Array(9).fill(null),
    currentPlayer: 'X',
    status: 'playing',
    winner: null,
    moveCount: 0,
    gameId: 'test-game',
  };

  const defaultProps = {
    gameState: mockGameState,
    disabled: false,
    onCellClick: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render 9 game cells', () => {
    render(<GameBoard {...defaultProps} />);

    const board = screen.getByTestId('game-board');
    expect(board).toBeInTheDocument();

    // Check that all 9 cells are rendered
    for (let i = 0; i < 9; i++) {
      expect(screen.getByTestId(`game-cell-${i}`)).toBeInTheDocument();
    }
  });

  it('should display board state correctly', () => {
    const gameStateWithMoves: GameState = {
      ...mockGameState,
      board: ['X', 'O', null, 'X', null, null, null, null, null],
    };

    render(<GameBoard {...defaultProps} gameState={gameStateWithMoves} />);

    expect(screen.getByTestId('game-cell-0')).toHaveTextContent('X');
    expect(screen.getByTestId('game-cell-1')).toHaveTextContent('O');
    expect(screen.getByTestId('game-cell-2')).toHaveTextContent('');
    expect(screen.getByTestId('game-cell-3')).toHaveTextContent('X');
  });

  it('should call onCellClick when cell is clicked', () => {
    const onCellClick = vi.fn();
    render(<GameBoard {...defaultProps} onCellClick={onCellClick} />);

    const cell = screen.getByTestId('game-cell-4');
    fireEvent.click(cell);

    expect(onCellClick).toHaveBeenCalledWith(4);
  });

  it('should disable all cells when disabled prop is true', () => {
    render(<GameBoard {...defaultProps} disabled={true} />);

    for (let i = 0; i < 9; i++) {
      const cell = screen.getByTestId(`game-cell-${i}`);
      expect(cell).toBeDisabled();
    }
  });

  it('should highlight winning cells', () => {
    const winningGameState: GameState = {
      ...mockGameState,
      board: ['X', 'X', 'X', null, null, null, null, null, null],
      status: 'won',
      winner: 'X',
    };

    render(<GameBoard {...defaultProps} gameState={winningGameState} />);

    // First row should be highlighted as winning
    expect(screen.getByTestId('game-cell-0')).toHaveClass('game-cell--winning');
    expect(screen.getByTestId('game-cell-1')).toHaveClass('game-cell--winning');
    expect(screen.getByTestId('game-cell-2')).toHaveClass('game-cell--winning');

    // Other cells should not be highlighted
    expect(screen.getByTestId('game-cell-3')).not.toHaveClass('game-cell--winning');
  });

  it('should not highlight cells when game is draw', () => {
    const drawGameState: GameState = {
      ...mockGameState,
      board: ['X', 'O', 'X', 'O', 'X', 'O', 'O', 'X', 'O'],
      status: 'draw',
      winner: null,
    };

    render(<GameBoard {...defaultProps} gameState={drawGameState} />);

    for (let i = 0; i < 9; i++) {
      expect(screen.getByTestId(`game-cell-${i}`)).not.toHaveClass('game-cell--winning');
    }
  });
});
