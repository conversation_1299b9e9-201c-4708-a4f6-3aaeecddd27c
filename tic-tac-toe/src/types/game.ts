/**
 * Game types and interfaces for Tic-Tac-Toe game
 */

export type Player = 'X' | 'O';
export type CellValue = Player | null;
export type GameStatus = 'playing' | 'won' | 'draw';

/**
 * Represents the current state of the game
 */
export interface GameState {
  /** Array of 9 cells representing the 3x3 board */
  board: CellValue[];
  /** Current player's turn */
  currentPlayer: Player;
  /** Current game status */
  status: GameStatus;
  /** Winner of the game (null if no winner yet) */
  winner: Player | null;
  /** Total number of moves made */
  moveCount: number;
  /** Game ID for tracking */
  gameId: string;
}

/**
 * Represents a move made by a player
 */
export interface GameMove {
  /** Position on the board (0-8) */
  position: number;
  /** Player making the move */
  player: Player;
  /** Timestamp of the move */
  timestamp: number;
}

/**
 * Game statistics and history
 */
export interface GameStats {
  /** Total games played */
  totalGames: number;
  /** Games won by X */
  xWins: number;
  /** Games won by O */
  oWins: number;
  /** Draw games */
  draws: number;
}

/**
 * Initial game state
 */
export const INITIAL_GAME_STATE: GameState = {
  board: Array(9).fill(null),
  currentPlayer: 'X',
  status: 'playing',
  winner: null,
  moveCount: 0,
  gameId: '',
};

/**
 * Winning combinations for the game
 */
export const WINNING_COMBINATIONS = [
  // Rows
  [0, 1, 2],
  [3, 4, 5],
  [6, 7, 8],
  // Columns
  [0, 3, 6],
  [1, 4, 7],
  [2, 5, 8],
  // Diagonals
  [0, 4, 8],
  [2, 4, 6],
] as const;
