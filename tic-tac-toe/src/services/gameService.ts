/**
 * Game service for handling game state operations
 * Simulates API calls with local storage persistence
 */

import { type GameState, type GameStats } from "../types/game";
import {
  generateGameId,
  makeMove as makeGameMove,
  resetGame as resetGameState,
} from "../utils/gameLogic";

const GAME_STATE_KEY = "tic-tac-toe-game-state";
const GAME_STATS_KEY = "tic-tac-toe-game-stats";

/**
 * Simulate API delay
 */
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * Get current game state from storage
 */
export const getGameState = async (): Promise<GameState> => {
  await delay(50); // Simulate API call

  const stored = localStorage.getItem(GAME_STATE_KEY);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch (error) {
      console.error("Error parsing stored game state:", error);
    }
  }

  // Return initial state if no stored state
  const initialState = resetGameState(generateGameId());
  await saveGameState(initialState);
  return initialState;
};

/**
 * Save game state to storage
 */
const saveGameState = async (gameState: GameState): Promise<void> => {
  await delay(20); // Simulate API call
  localStorage.setItem(GAME_STATE_KEY, JSON.stringify(gameState));
};

/**
 * Make a move in the game
 */
export const makeMove = async (
  gameState: GameState,
  position: number
): Promise<GameState> => {
  await delay(30); // Simulate API call

  try {
    const newGameState = makeGameMove(gameState, position);
    await saveGameState(newGameState);

    // Update stats if game ended
    if (newGameState.status !== "playing") {
      await updateGameStats(newGameState);
    }

    return newGameState;
  } catch (error) {
    throw new Error(
      `Failed to make move: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
};

/**
 * Reset the game
 */
export const resetGame = async (): Promise<GameState> => {
  await delay(30); // Simulate API call

  const newGameState = resetGameState(generateGameId());
  await saveGameState(newGameState);
  return newGameState;
};

/**
 * Get game statistics
 */
export const getGameStats = async (): Promise<GameStats> => {
  await delay(20); // Simulate API call

  const stored = localStorage.getItem(GAME_STATS_KEY);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch (error) {
      console.error("Error parsing stored game stats:", error);
    }
  }

  // Return initial stats
  const initialStats: GameStats = {
    totalGames: 0,
    xWins: 0,
    oWins: 0,
    draws: 0,
  };

  localStorage.setItem(GAME_STATS_KEY, JSON.stringify(initialStats));
  return initialStats;
};

/**
 * Update game statistics
 */
const updateGameStats = async (gameState: GameState): Promise<void> => {
  const currentStats = await getGameStats();

  const newStats: GameStats = {
    ...currentStats,
    totalGames: currentStats.totalGames + 1,
  };

  if (gameState.status === "won" && gameState.winner) {
    if (gameState.winner === "X") {
      newStats.xWins += 1;
    } else {
      newStats.oWins += 1;
    }
  } else if (gameState.status === "draw") {
    newStats.draws += 1;
  }

  localStorage.setItem(GAME_STATS_KEY, JSON.stringify(newStats));
};

/**
 * Clear all game data
 */
export const clearGameData = async (): Promise<void> => {
  await delay(20); // Simulate API call
  localStorage.removeItem(GAME_STATE_KEY);
  localStorage.removeItem(GAME_STATS_KEY);
};
